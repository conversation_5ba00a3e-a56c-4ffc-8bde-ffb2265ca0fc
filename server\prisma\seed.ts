
import { PrismaClient, Role } from '@prisma/client';
import bcrypt from 'bcrypt';
import { readFileSync } from 'fs';
import { join } from 'path';

const prisma = new PrismaClient();

// Load JSON data files
const categoriesData = JSON.parse(
  readFileSync(join(__dirname, 'seed-data/CATEGORIES_DATA.json'), 'utf-8')
);

const tagsData = JSON.parse(
  readFileSync(join(__dirname, 'seed-data/TAGS_DATA.json'), 'utf-8')
);

const usersData = JSON.parse(
  readFileSync(join(__dirname, 'seed-data/USERS_DATA.json'), 'utf-8')
);

const postsData = JSON.parse(
  readFileSync(join(__dirname, 'seed-data/POSTS_DATA.json'), 'utf-8')
);

const commentsData = JSON.parse(
  readFileSync(join(__dirname, 'seed-data/COMMENTS_DATA.json'), 'utf-8')
);

async function main() {
  console.log('🌱 Starting database seeding...');

  // Clear existing data (optional - comment out if you want to keep existing data)
  console.log('🧹 Cleaning existing data...');
  await prisma.postViews.deleteMany();
  await prisma.comment.deleteMany();
  await prisma.post.deleteMany();
  await prisma.category.deleteMany();
  await prisma.tag.deleteMany();
  await prisma.user.deleteMany();

  // Create Categories from JSON
  console.log('📂 Creating categories...');
  const categories = await Promise.all(
    categoriesData.map((categoryData: any) =>
      prisma.category.create({ data: categoryData })
    )
  );

  // Create Tags from JSON
  console.log('🏷️ Creating tags...');
  const tags = await Promise.all(
    tagsData.map((tagData: any) => prisma.tag.create({ data: tagData }))
  );

  // Hash password for users
  const hashedPassword = await bcrypt.hash('password123', 10);

  // Create Users (including admin)
  console.log('👥 Creating users...');
  const admin = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      password: hashedPassword,
      name: 'Admin User',
      role: Role.ADMIN,
      bio: 'Administrator of The Salty Devs blog. Passionate about web development and sharing knowledge.',
    },
  });

  // Create Users from JSON
  console.log('👤 Creating users from JSON...');
  const users = await Promise.all(
    usersData.map((userData: any) =>
      prisma.user.create({
        data: {
          ...userData,
          password: hashedPassword,
          role: Role.USER,
        },
      })
    )
  );

  // Create Posts with realistic content
  console.log('📝 Creating posts...');
  const mockPosts = [
    {
      title: 'Getting Started with React Hooks',
      content: `React Hooks have revolutionized the way we write React components. In this comprehensive guide, we'll explore the most commonly used hooks and how they can simplify your code.

## useState Hook

The useState hook is the most fundamental hook in React. It allows you to add state to functional components:

\`\`\`javascript
const [count, setCount] = useState(0);
\`\`\`

## useEffect Hook

The useEffect hook lets you perform side effects in functional components. It's like componentDidMount, componentDidUpdate, and componentWillUnmount combined.

## Custom Hooks

One of the most powerful features of hooks is the ability to create custom hooks that encapsulate stateful logic and make it reusable across components.

Whether you're new to React or looking to modernize your existing codebase, understanding hooks is essential for modern React development.`,
      published: true,
      imageURL: 'https://picsum.photos/800/400?random=1',
    },
    {
      title: 'TypeScript Best Practices for 2024',
      content: `TypeScript has become an essential tool for JavaScript developers. Here are the best practices that will make your TypeScript code more maintainable and robust.

## Strict Mode Configuration

Always enable strict mode in your tsconfig.json:

\`\`\`json
{
  "compilerOptions": {
    "strict": true,
    "noImplicitAny": true,
    "strictNullChecks": true
  }
}
\`\`\`

## Type Definitions

Create clear and reusable type definitions:

\`\`\`typescript
interface User {
  id: string;
  name: string;
  email: string;
  role: 'admin' | 'user';
}
\`\`\`

## Generic Types

Use generics to create flexible and reusable components and functions.

Following these practices will help you write better TypeScript code and catch errors early in development.`,
      published: true,
      imageURL: 'https://picsum.photos/800/400?random=2',
    },
    {
      title: 'Building RESTful APIs with Node.js and Express',
      content: `Creating robust RESTful APIs is a fundamental skill for backend developers. In this tutorial, we'll build a complete API using Node.js and Express.

## Project Setup

Start by initializing your project and installing dependencies:

\`\`\`bash
npm init -y
npm install express cors helmet morgan
npm install -D nodemon @types/node typescript
\`\`\`

## Basic Server Setup

Create a basic Express server with proper middleware:

\`\`\`javascript
const express = require('express');
const cors = require('cors');
const helmet = require('helmet');

const app = express();

app.use(helmet());
app.use(cors());
app.use(express.json());
\`\`\`

## Route Organization

Organize your routes using Express Router for better maintainability.

## Error Handling

Implement proper error handling middleware to catch and respond to errors gracefully.

This foundation will serve you well for building scalable APIs.`,
      published: true,
      imageURL: 'https://picsum.photos/800/400?random=3',
    },
  ];

  const posts = [];
  for (let i = 0; i < mockPosts.length; i++) {
    const postData = mockPosts[i];
    const randomUser = users[Math.floor(Math.random() * users.length)];
    const randomCategory =
      categories[Math.floor(Math.random() * categories.length)];

    const post = await prisma.post.create({
      data: {
        ...postData,
        authorId: i === 0 ? admin.id : randomUser.id, // First post by admin
        categoryId: randomCategory.id,
        publishedAt: postData.published ? new Date() : null,
        tags: {
          connect: tags
            .slice(0, Math.floor(Math.random() * 3) + 1)
            .map((tag) => ({ id: tag.id })),
        },
      },
    });
    posts.push(post);
  }

  // Create Comments
  console.log('💬 Creating comments...');
  const mockComments = [
    'Great article! This really helped me understand the concept better.',
    "Thanks for sharing this. I've been struggling with this exact problem.",
    'Excellent explanation. Looking forward to more content like this.',
    'This is exactly what I needed. Clear and concise tutorial.',
    'Amazing work! Could you do a follow-up on advanced topics?',
    "Very helpful post. I'll definitely be implementing this in my project.",
    'Love the code examples. They make everything so much clearer.',
    'This saved me hours of debugging. Thank you!',
    'Great tutorial! Any plans for a video version?',
    'Perfect timing! I was just working on something similar.',
  ];

  for (const post of posts) {
    const numComments = Math.floor(Math.random() * 5) + 1; // 1-5 comments per post
    for (let i = 0; i < numComments; i++) {
      const randomUser = users[Math.floor(Math.random() * users.length)];
      const randomComment =
        mockComments[Math.floor(Math.random() * mockComments.length)];

      await prisma.comment.create({
        data: {
          content: randomComment,
          authorId: randomUser.id,
          postId: post.id,
        },
      });
    }
  }

  console.log('✅ Seed data created successfully!');
  console.log(`📊 Created:`);
  console.log(`   - ${categories.length} categories`);
  console.log(`   - ${tags.length} tags`);
  console.log(`   - ${users.length + 1} users (including admin)`);
  console.log(`   - ${posts.length} posts`);
  console.log(`   - Multiple comments per post`);
  console.log(`   - Admin login: <EMAIL> / password123`);
}

main()
  .catch((e) => {
    console.error('❌ Error seeding database:', e);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
